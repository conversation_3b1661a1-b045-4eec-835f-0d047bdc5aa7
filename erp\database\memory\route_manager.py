"""
Route management for database memory registry
"""
import asyncio
import sys
from typing import Dict, Optional, Any, List
from ...logging import get_logger


class RouteManager:
    """Manages route registration and discovery for database registry"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.routes: Dict[str, Dict] = {}  # Store HTTP routes for this database
        self._routes_registered = False
        self._lock = asyncio.Lock()
        self._logger = get_logger(f"{__name__}.{db_name}")

    async def ensure_routes_registered(self, addon_load_order: List[str] = None) -> None:
        """Ensure routes are registered for this database (lazy loading)"""
        if self._routes_registered:
            return

        async with self._lock:
            if self._routes_registered:
                return

            try:
                # Discover and register addon routes
                await self._discover_addon_routes(addon_load_order)

                # Register routes with FastAPI
                await self._register_with_fastapi()

                self._routes_registered = True
                self._logger.info(f"Routes registered for database: {self.db_name}")
            except Exception as e:
                self._logger.debug(f"Route registration failed for {self.db_name}: {e}")
                # Don't raise - routes will be registered later when addons are loaded

    async def _register_with_fastapi(self) -> None:
        """Register discovered routes with FastAPI application"""
        try:
            from .registry_manager import MemoryRegistryManager
            from ...http.fastapi_integration import RouteIntegration

            app = getattr(MemoryRegistryManager, '_app', None)
            if not app:
                self._logger.debug(f"No FastAPI app available for {self.db_name}")
                return

            # Use internal method to get routes without acquiring lock (since lock is already held)
            routes = self.routes.copy()
            if routes:
                RouteIntegration._register_routes_from_registry(app, routes, f"db_{self.db_name}")
                self._logger.info(f"Registered {len(routes)} routes with FastAPI for {self.db_name}")

        except Exception as e:
            self._logger.error(f"FastAPI registration failed for {self.db_name}: {e}")

    async def _discover_addon_routes(self, addon_load_order: List[str] = None) -> None:
        """Optimized route discovery for this database in addon load order"""
        self._logger.debug(f"Discovering routes for database: {self.db_name}")

        # Use cached route discovery for better performance, but respect addon order
        route_count = 0

        # Process addons in installation order if provided
        if addon_load_order:
            for addon_name in addon_load_order:
                route_count += await self._discover_addon_routes_for_addon(addon_name)
        else:
            # Fallback to discovering all addon routes
            route_count += await self._discover_all_addon_routes()

        self._logger.info(f"Discovered {route_count} routes for database: {self.db_name}")

    async def _discover_addon_routes_for_addon(self, addon_name: str) -> int:
        """Discover routes for a specific addon"""
        route_count = 0
        try:
            # Check main addon module
            main_module_name = f'erp.addons.{addon_name}'
            route_count += self._extract_routes_from_module_name(main_module_name)

            # Check controllers module
            controllers_module_name = f'{main_module_name}.controllers'
            route_count += self._extract_routes_from_module_name(controllers_module_name)

            # Check individual controller modules within the controllers package
            route_count += await self._discover_individual_controllers(addon_name)

        except Exception as e:
            self._logger.warning(f"Failed to discover routes for addon {addon_name}: {e}")

        return route_count

    async def _discover_individual_controllers(self, addon_name: str) -> int:
        """Discover routes from individual controller modules within an addon"""
        route_count = 0
        try:
            # Look for all modules that match the pattern erp.addons.{addon_name}.controllers.*
            controllers_prefix = f'erp.addons.{addon_name}.controllers.'

            for module_name in sys.modules:
                if module_name.startswith(controllers_prefix) and module_name != f'erp.addons.{addon_name}.controllers':
                    # This is an individual controller module
                    module = sys.modules[module_name]
                    if module:
                        discovered = self._extract_routes_from_module(module)
                        route_count += discovered
                        if discovered > 0:
                            self._logger.debug(f"Found {discovered} routes in {module_name}")

        except Exception as e:
            self._logger.warning(f"Failed to discover individual controllers for {addon_name}: {e}")

        return route_count

    async def _discover_all_addon_routes(self) -> int:
        """Discover routes from all loaded addon modules"""
        route_count = 0
        try:
            for module_name, module in sys.modules.items():
                if self._is_addon_module(module_name) and module:
                    route_count += self._extract_routes_from_module(module)
        except Exception as e:
            self._logger.error(f"Failed to discover all addon routes: {e}")
        return route_count

    def _extract_routes_from_module_name(self, module_name: str) -> int:
        """Extract routes from a module by name"""
        if module_name in sys.modules:
            module = sys.modules[module_name]
            return self._extract_routes_from_module(module)
        return 0

    def _extract_routes_from_module(self, module) -> int:
        """Extract routes from a module"""
        route_count = 0
        if hasattr(module, '_route_handlers'):
            for handler in module._route_handlers:
                if hasattr(handler, '_route_metadata'):
                    route_metadata = handler._route_metadata
                    path = route_metadata['path']

                    # Register the route for this database (using internal method since lock is already held)
                    self._register_route_internal(path, handler, **{
                        k: v for k, v in route_metadata.items()
                        if k not in ['path', 'original_func']
                    })
                    route_count += 1
        return route_count

    def _is_addon_module(self, module_name: str) -> bool:
        """Check if module name is an addon module"""
        return (module_name.startswith('erp.addons.') or
                module_name.startswith('addons.')) and module_name != 'addons'

    async def register_route(self, path: str, handler: Any, **kwargs) -> None:
        """Register a route handler for this database"""
        async with self._lock:
            self._register_route_internal(path, handler, **kwargs)

    def _register_route_internal(self, path: str, handler: Any, **kwargs) -> None:
        """Internal route registration without lock (for use when lock is already held)"""
        route_info = {
            'handler': handler,
            'path': path,
            **kwargs
        }
        self.routes[path] = route_info
        self._logger.debug(f"Registered route for {self.db_name}: {kwargs.get('methods', ['GET'])} {path}")

    async def get_routes(self) -> Dict[str, Dict]:
        """Get all registered routes for this database"""
        # Don't call ensure_routes_registered here to avoid circular dependency
        async with self._lock:
            return self.routes.copy()

    async def get_route(self, path: str) -> Optional[Dict]:
        """Get specific route by path"""
        async with self._lock:
            return self.routes.get(path)

    async def remove_route(self, path: str) -> bool:
        """Remove a route by path"""
        async with self._lock:
            if path in self.routes:
                del self.routes[path]
                self._logger.debug(f"Removed route for {self.db_name}: {path}")
                return True
            return False

    async def clear_routes(self) -> None:
        """Clear all routes for this database"""
        async with self._lock:
            self.routes.clear()
            self._logger.debug(f"Cleared all routes for {self.db_name}")

    def get_route_stats(self) -> Dict[str, Any]:
        """Get route statistics"""
        return {
            'routes_count': len(self.routes),
            'routes_registered': self._routes_registered
        }

    def reset_route_state(self) -> None:
        """Reset route registration state"""
        self._routes_registered = False
        self.routes.clear()
        self._logger.debug("Route state reset")
